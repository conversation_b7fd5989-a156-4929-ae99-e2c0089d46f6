#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速启动脚本
"""

import asyncio
from simple_requester import SimpleHTTPRequester


def main():
    print("🚀 HTTP高频请求工具")
    print("=" * 50)
    
    # 默认配置
    url = "https://item.btime.com/543emf2596h8fdanmiv21bmiosu"
    requests = 2000
    time_limit = 60
    concurrent = 100
    
    print(f"目标URL: {url}")
    print(f"请求次数: {requests}")
    print(f"时间限制: {time_limit}秒")
    print(f"并发数: {concurrent}")
    print("-" * 50)
    
    # 询问是否修改配置
    modify = input("是否修改配置? (y/N): ").strip().lower()
    
    if modify in ['y', 'yes']:
        try:
            new_requests = input(f"请求次数 (默认{requests}): ").strip()
            if new_requests:
                requests = int(new_requests)
            
            new_time = input(f"时间限制/秒 (默认{time_limit}): ").strip()
            if new_time:
                time_limit = int(new_time)
            
            new_concurrent = input(f"并发数 (默认{concurrent}): ").strip()
            if new_concurrent:
                concurrent = int(new_concurrent)
                
        except ValueError:
            print("输入无效，使用默认配置")
    
    print("\n⚠️  注意事项:")
    print("- 请确保您有权限访问目标网站")
    print("- 高频请求可能会被服务器限制")
    print("- 请遵守网站使用条款")
    
    confirm = input("\n确认开始请求? (y/N): ").strip().lower()
    
    if confirm in ['y', 'yes']:
        # 创建请求器并运行
        requester = SimpleHTTPRequester(url, requests, time_limit, concurrent)
        asyncio.run(requester.run())
    else:
        print("已取消")


if __name__ == "__main__":
    main()
