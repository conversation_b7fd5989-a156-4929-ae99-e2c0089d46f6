#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版HTTP自动请求程序
专门用于快速访问指定URL 2000次/分钟
"""

import asyncio
import aiohttp
import time
import random
from datetime import datetime


class SimpleHTTPRequester:
    def __init__(self, url, total_requests=2000, time_limit=60, concurrent=100):
        self.url = url
        self.total_requests = total_requests
        self.time_limit = time_limit
        self.concurrent = concurrent
        self.success_count = 0
        self.failed_count = 0
        self.start_time = None
        
        # 模拟浏览器的User-Agent列表
        self.user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15"
        ]
    
    def get_headers(self):
        """获取随机请求头"""
        return {
            "User-Agent": random.choice(self.user_agents),
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Accept-Encoding": "gzip, deflate, br",
            "DNT": "1",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
        }
    
    async def make_request(self, session, semaphore, request_id):
        """发送单个请求"""
        async with semaphore:
            try:
                headers = self.get_headers()
                async with session.get(self.url, headers=headers, timeout=5) as response:
                    await response.text()  # 读取响应内容
                    self.success_count += 1
                    return f"请求 {request_id}: 成功 (状态码: {response.status})"
            except Exception as e:
                self.failed_count += 1
                return f"请求 {request_id}: 失败 ({str(e)})"
    
    async def run(self):
        """运行请求"""
        print(f"开始请求: {self.url}")
        print(f"目标: {self.total_requests} 次请求在 {self.time_limit} 秒内完成")
        print(f"并发数: {self.concurrent}")
        print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("-" * 60)
        
        self.start_time = time.time()
        
        # 创建信号量控制并发数
        semaphore = asyncio.Semaphore(self.concurrent)
        
        # 设置连接器
        connector = aiohttp.TCPConnector(
            limit=self.concurrent * 2,  # 连接池大小
            limit_per_host=self.concurrent,
            ttl_dns_cache=300,
            use_dns_cache=True,
        )
        
        timeout = aiohttp.ClientTimeout(total=5)
        
        async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
            # 创建所有任务
            tasks = []
            for i in range(self.total_requests):
                task = asyncio.create_task(
                    self.make_request(session, semaphore, i + 1)
                )
                tasks.append(task)
            
            # 监控进度
            progress_task = asyncio.create_task(self.monitor_progress())
            
            try:
                # 等待所有请求完成或超时
                await asyncio.wait_for(
                    asyncio.gather(*tasks, return_exceptions=True),
                    timeout=self.time_limit
                )
            except asyncio.TimeoutError:
                print(f"\n⏰ 时间限制 {self.time_limit} 秒已到，停止请求...")
                # 取消未完成的任务
                for task in tasks:
                    if not task.done():
                        task.cancel()
            finally:
                progress_task.cancel()
        
        self.print_final_stats()
    
    async def monitor_progress(self):
        """监控进度"""
        try:
            while True:
                await asyncio.sleep(5)  # 每5秒更新一次
                elapsed = time.time() - self.start_time
                total_completed = self.success_count + self.failed_count
                rate = total_completed / elapsed if elapsed > 0 else 0
                
                print(f"⏱️  {elapsed:.1f}s | 完成: {total_completed}/{self.total_requests} | "
                      f"成功: {self.success_count} | 失败: {self.failed_count} | "
                      f"速率: {rate:.1f}/s")
        except asyncio.CancelledError:
            pass
    
    def print_final_stats(self):
        """打印最终统计"""
        end_time = time.time()
        total_time = end_time - self.start_time
        total_requests = self.success_count + self.failed_count
        
        print("\n" + "=" * 60)
        print("🎯 最终统计结果")
        print("=" * 60)
        print(f"📊 总请求数: {total_requests}")
        print(f"✅ 成功请求: {self.success_count}")
        print(f"❌ 失败请求: {self.failed_count}")
        print(f"📈 成功率: {(self.success_count/total_requests*100):.2f}%" if total_requests > 0 else "0%")
        print(f"⏱️  总用时: {total_time:.2f} 秒")
        print(f"🚀 平均速率: {(total_requests/total_time):.2f} 请求/秒" if total_time > 0 else "N/A")
        print(f"🎯 目标达成: {'✅ 是' if total_requests >= self.total_requests else '❌ 否'}")
        print("=" * 60)


async def main():
    """主函数"""
    # 目标URL
    url = "https://item.btime.com/543emf2596h8fdanmiv21bmiosu"
    
    print("🚀 HTTP高频请求程序")
    print("=" * 60)
    
    # 创建请求器实例
    requester = SimpleHTTPRequester(
        url=url,
        total_requests=2000,  # 2000次请求
        time_limit=60,        # 60秒内完成
        concurrent=100        # 100个并发连接
    )
    
    # 开始请求
    await requester.run()


if __name__ == "__main__":
    print("⚡ 准备启动高频HTTP请求...")
    print("⚠️  注意: 请确保您有权限访问目标网站")
    print("⚠️  高频请求可能会被服务器限制，请谨慎使用")
    
    # 确认启动
    confirm = input("\n是否继续? (y/N): ").strip().lower()
    if confirm in ['y', 'yes']:
        asyncio.run(main())
    else:
        print("程序已取消")
