#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTTP自动请求程序
模拟浏览器访问指定URL，实现高频率访问
"""

import asyncio
import aiohttp
import time
import random
import threading
from concurrent.futures import Thr<PERSON><PERSON><PERSON><PERSON>xecutor, as_completed
from collections import defaultdict
from config import TARGET_URL, REQUEST_CONFIG, BROWSER_CONFIG


class HTTPRequester:
    def __init__(self):
        self.target_url = TARGET_URL
        self.config = REQUEST_CONFIG
        self.browser_config = BROWSER_CONFIG
        self.stats = defaultdict(int)
        self.start_time = None
        self.lock = threading.Lock()
        
    def get_random_headers(self):
        """获取随机的请求头"""
        headers = self.browser_config["headers"].copy()
        headers["User-Agent"] = random.choice(self.browser_config["user_agents"])
        return headers
    
    def make_sync_request(self, session, request_id):
        """同步请求方法"""
        try:
            headers = self.get_random_headers()
            response = session.get(
                self.target_url,
                headers=headers,
                timeout=self.config["timeout"],
                allow_redirects=True
            )
            
            with self.lock:
                self.stats["success"] += 1
                self.stats["total"] += 1
                
            return {
                "id": request_id,
                "status": "success",
                "status_code": response.status_code,
                "response_time": response.elapsed.total_seconds()
            }
            
        except Exception as e:
            with self.lock:
                self.stats["failed"] += 1
                self.stats["total"] += 1
                
            return {
                "id": request_id,
                "status": "failed",
                "error": str(e)
            }
    
    async def make_async_request(self, session, request_id):
        """异步请求方法"""
        try:
            headers = self.get_random_headers()
            start_time = time.time()
            
            async with session.get(
                self.target_url,
                headers=headers,
                timeout=aiohttp.ClientTimeout(total=self.config["timeout"])
            ) as response:
                await response.text()  # 读取响应内容
                response_time = time.time() - start_time
                
                self.stats["success"] += 1
                self.stats["total"] += 1
                
                return {
                    "id": request_id,
                    "status": "success",
                    "status_code": response.status,
                    "response_time": response_time
                }
                
        except Exception as e:
            self.stats["failed"] += 1
            self.stats["total"] += 1
            
            return {
                "id": request_id,
                "status": "failed",
                "error": str(e)
            }
    
    def run_sync_requests(self):
        """使用同步方式运行请求"""
        import requests
        
        print(f"开始同步请求模式...")
        print(f"目标URL: {self.target_url}")
        print(f"总请求数: {self.config['total_requests']}")
        print(f"并发线程数: {self.config['concurrent_threads']}")
        print(f"时间限制: {self.config['time_limit']}秒")
        print("-" * 50)
        
        self.start_time = time.time()
        
        with requests.Session() as session:
            with ThreadPoolExecutor(max_workers=self.config["concurrent_threads"]) as executor:
                # 提交所有任务
                futures = []
                for i in range(self.config["total_requests"]):
                    future = executor.submit(self.make_sync_request, session, i + 1)
                    futures.append(future)
                
                # 处理完成的任务
                completed = 0
                for future in as_completed(futures):
                    result = future.result()
                    completed += 1
                    
                    # 检查时间限制
                    elapsed_time = time.time() - self.start_time
                    if elapsed_time >= self.config["time_limit"]:
                        print(f"\n时间限制已达到 ({elapsed_time:.2f}秒)，停止请求...")
                        # 取消未完成的任务
                        for f in futures:
                            f.cancel()
                        break
                    
                    # 显示进度
                    if completed % 100 == 0 or completed == self.config["total_requests"]:
                        self.print_progress(elapsed_time)
        
        self.print_final_stats()
    
    async def run_async_requests(self):
        """使用异步方式运行请求"""
        print(f"开始异步请求模式...")
        print(f"目标URL: {self.target_url}")
        print(f"总请求数: {self.config['total_requests']}")
        print(f"并发数: {self.config['concurrent_threads']}")
        print(f"时间限制: {self.config['time_limit']}秒")
        print("-" * 50)
        
        self.start_time = time.time()
        
        # 创建连接器，限制并发连接数
        connector = aiohttp.TCPConnector(limit=self.config["concurrent_threads"])
        
        async with aiohttp.ClientSession(connector=connector) as session:
            # 创建信号量来控制并发数
            semaphore = asyncio.Semaphore(self.config["concurrent_threads"])
            
            async def bounded_request(request_id):
                async with semaphore:
                    return await self.make_async_request(session, request_id)
            
            # 创建所有任务
            tasks = []
            for i in range(self.config["total_requests"]):
                task = asyncio.create_task(bounded_request(i + 1))
                tasks.append(task)
            
            # 等待任务完成或超时
            try:
                await asyncio.wait_for(
                    asyncio.gather(*tasks, return_exceptions=True),
                    timeout=self.config["time_limit"]
                )
            except asyncio.TimeoutError:
                print(f"\n时间限制已达到，停止请求...")
                # 取消未完成的任务
                for task in tasks:
                    if not task.done():
                        task.cancel()
        
        self.print_final_stats()
    
    def print_progress(self, elapsed_time):
        """打印进度信息"""
        total = self.stats["total"]
        success = self.stats["success"]
        failed = self.stats["failed"]
        rate = total / elapsed_time if elapsed_time > 0 else 0
        
        print(f"进度: {total}/{self.config['total_requests']} | "
              f"成功: {success} | 失败: {failed} | "
              f"速率: {rate:.1f}请求/秒 | "
              f"用时: {elapsed_time:.1f}秒")
    
    def print_final_stats(self):
        """打印最终统计信息"""
        end_time = time.time()
        total_time = end_time - self.start_time
        
        print("\n" + "=" * 50)
        print("请求完成统计:")
        print(f"总请求数: {self.stats['total']}")
        print(f"成功请求: {self.stats['success']}")
        print(f"失败请求: {self.stats['failed']}")
        print(f"成功率: {(self.stats['success'] / self.stats['total'] * 100):.2f}%" if self.stats['total'] > 0 else "0%")
        print(f"总用时: {total_time:.2f}秒")
        print(f"平均速率: {(self.stats['total'] / total_time):.2f}请求/秒" if total_time > 0 else "N/A")
        print("=" * 50)


def main():
    """主函数"""
    requester = HTTPRequester()
    
    print("HTTP自动请求程序")
    print("=" * 50)
    
    # 选择运行模式
    mode = input("选择运行模式 (1: 同步模式, 2: 异步模式) [默认: 2]: ").strip()
    
    if mode == "1":
        requester.run_sync_requests()
    else:
        # 异步模式
        asyncio.run(requester.run_async_requests())


if __name__ == "__main__":
    main()
