# HTTP自动请求程序

这是一个Python程序，用于模拟浏览器自动访问指定的URL，实现高频率的HTTP请求。

## 功能特点

- 🚀 支持同步和异步两种请求模式
- 🎭 模拟真实浏览器行为（随机User-Agent、完整请求头）
- ⚡ 高并发请求，可在1分钟内完成2000次访问
- 📊 实时统计和进度显示
- ⚙️ 可配置的请求参数
- 🛡️ 错误处理和重试机制

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 直接运行
```bash
python http_requester.py
```

### 2. 配置参数
编辑 `config.py` 文件来修改请求参数：

- `TARGET_URL`: 目标URL
- `total_requests`: 总请求次数（默认2000）
- `time_limit`: 时间限制（默认60秒）
- `concurrent_threads`: 并发线程数（默认50）
- `timeout`: 请求超时时间（默认10秒）

### 3. 运行模式选择

程序支持两种运行模式：

#### 同步模式 (选项1)
- 使用 `requests` 库和线程池
- 适合稳定的网络环境
- 资源占用相对较低

#### 异步模式 (选项2) - 推荐
- 使用 `aiohttp` 库和异步协程
- 性能更高，适合高并发场景
- 能更快达到目标请求数量

## 配置说明

### 请求配置 (REQUEST_CONFIG)
```python
REQUEST_CONFIG = {
    "total_requests": 2000,      # 总请求次数
    "time_limit": 60,            # 时间限制（秒）
    "concurrent_threads": 50,    # 并发数
    "timeout": 10,               # 请求超时时间
    "max_retries": 3,            # 重试次数
    "request_interval": 0,       # 请求间隔
}
```

### 浏览器模拟配置 (BROWSER_CONFIG)
程序会随机选择不同的User-Agent来模拟不同的浏览器，包括：
- Chrome (Windows/Mac)
- Firefox
- Safari
- Chrome (Linux)

## 输出示例

```
HTTP自动请求程序
==================================================
开始异步请求模式...
目标URL: https://item.btime.com/543emf2596h8fdanmiv21bmiosu
总请求数: 2000
并发数: 50
时间限制: 60秒
--------------------------------------------------
进度: 100/2000 | 成功: 98 | 失败: 2 | 速率: 45.2请求/秒 | 用时: 2.2秒
进度: 200/2000 | 成功: 195 | 失败: 5 | 速率: 48.1请求/秒 | 用时: 4.2秒
...
==================================================
请求完成统计:
总请求数: 2000
成功请求: 1987
失败请求: 13
成功率: 99.35%
总用时: 42.5秒
平均速率: 47.1请求/秒
==================================================
```

## 注意事项

⚠️ **重要提醒**：
1. 请确保您有权限访问目标网站
2. 高频率请求可能会被目标服务器限制或封禁IP
3. 请遵守网站的robots.txt和使用条款
4. 建议在测试环境中先进行小规模测试
5. 如果遇到反爬虫机制，可能需要添加代理或降低请求频率

## 故障排除

### 常见问题

1. **连接超时**
   - 增加 `timeout` 值
   - 减少 `concurrent_threads` 数量

2. **请求被拒绝**
   - 目标网站可能有反爬虫机制
   - 尝试降低并发数和请求频率

3. **内存占用过高**
   - 减少并发数
   - 使用同步模式

## 许可证

本项目仅供学习和测试使用，请勿用于恶意攻击或违法用途。
