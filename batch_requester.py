#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量请求脚本 - 支持多种预设模式
"""

import asyncio
import sys
from simple_requester import SimpleHTTPRequester


class BatchRequester:
    def __init__(self):
        self.url = "https://item.btime.com/543emf2596h8fdanmiv21bmiosu"
        
        # 预设模式
        self.presets = {
            "1": {
                "name": "标准模式 (2000次/60秒)",
                "requests": 2000,
                "time_limit": 60,
                "concurrent": 100
            },
            "2": {
                "name": "快速模式 (1000次/30秒)",
                "requests": 1000,
                "time_limit": 30,
                "concurrent": 80
            },
            "3": {
                "name": "温和模式 (500次/60秒)",
                "requests": 500,
                "time_limit": 60,
                "concurrent": 30
            },
            "4": {
                "name": "测试模式 (100次/10秒)",
                "requests": 100,
                "time_limit": 10,
                "concurrent": 20
            },
            "5": {
                "name": "极速模式 (2000次/30秒)",
                "requests": 2000,
                "time_limit": 30,
                "concurrent": 150
            }
        }
    
    def show_menu(self):
        """显示菜单"""
        print("🚀 HTTP批量请求工具")
        print("=" * 60)
        print(f"目标URL: {self.url}")
        print("=" * 60)
        print("请选择运行模式:")
        
        for key, preset in self.presets.items():
            print(f"{key}. {preset['name']}")
            print(f"   └─ {preset['requests']}次请求, {preset['time_limit']}秒内完成, {preset['concurrent']}并发")
        
        print("6. 自定义模式")
        print("0. 退出")
        print("-" * 60)
    
    def get_custom_config(self):
        """获取自定义配置"""
        try:
            requests = int(input("请求次数: "))
            time_limit = int(input("时间限制(秒): "))
            concurrent = int(input("并发数: "))
            
            return {
                "name": "自定义模式",
                "requests": requests,
                "time_limit": time_limit,
                "concurrent": concurrent
            }
        except ValueError:
            print("❌ 输入无效")
            return None
    
    async def run_preset(self, config):
        """运行预设配置"""
        print(f"\n🎯 启动 {config['name']}")
        print("-" * 40)
        
        requester = SimpleHTTPRequester(
            self.url,
            config['requests'],
            config['time_limit'],
            config['concurrent']
        )
        
        await requester.run()
    
    async def main(self):
        """主函数"""
        while True:
            self.show_menu()
            
            choice = input("请选择 (0-6): ").strip()
            
            if choice == "0":
                print("👋 再见!")
                break
            elif choice in self.presets:
                config = self.presets[choice]
                print(f"\n✅ 已选择: {config['name']}")
                
                confirm = input("确认执行? (y/N): ").strip().lower()
                if confirm in ['y', 'yes']:
                    await self.run_preset(config)
                else:
                    print("已取消")
                    
            elif choice == "6":
                config = self.get_custom_config()
                if config:
                    print(f"\n✅ 自定义配置: {config['requests']}次/{config['time_limit']}秒/{config['concurrent']}并发")
                    confirm = input("确认执行? (y/N): ").strip().lower()
                    if confirm in ['y', 'yes']:
                        await self.run_preset(config)
                    else:
                        print("已取消")
            else:
                print("❌ 无效选择")
            
            if choice != "0":
                input("\n按回车键继续...")
                print("\n" * 2)


def main():
    """入口函数"""
    try:
        batch_requester = BatchRequester()
        asyncio.run(batch_requester.main())
    except KeyboardInterrupt:
        print("\n\n👋 程序已中断")
    except Exception as e:
        print(f"\n❌ 程序出错: {e}")


if __name__ == "__main__":
    main()
